# Vendor Assets Integration

Dự án này sử dụng Vite để sao chép các thư viện vendor từ `node_modules` ra `public/vendor/`:

- **Tabler Core**: To<PERSON><PERSON> bộ thư mục `dist` của `@tabler/core`
- **jQuery**: Các file jQuery từ `jquery/dist`
- **Bootstrap**: Toàn bộ thư mục `dist` của `bootstrap`
- **Bootstrap Icons**: Th<PERSON> mục `font` và `icons` của `bootstrap-icons`

## Cấu trúc thư mục

```text
public/vendor/
├── tabler/                 # Tabler Core assets
│   ├── css/               # Tất cả CSS files của Tabler
│   │   ├── tabler.min.css # CSS chính
│   │   ├── tabler-flags.min.css
│   │   ├── tabler-payments.min.css
│   │   ├── tabler-socials.min.css
│   │   ├── tabler-themes.min.css
│   │   └── ... (và các variants RTL, source maps)
│   ├── js/                # Tất cả JavaScript files của Tabler
│   │   ├── tabler.min.js  # JS chính
│   │   ├── tabler-theme.min.js
│   │   └── ... (và các variants ESM, source maps)
│   ├── img/               # Tất cả hình ảnh
│   │   ├── flags/         # Cờ các quốc gia
│   │   ├── payments/      # Icons thanh toán
│   │   └── social/        # Icons mạng xã hội
│   ├── libs/              # Thư viện bên thứ 3
│   │   ├── apexcharts/
│   │   ├── bootstrap/
│   │   ├── fullcalendar/
│   │   └── ... (20+ thư viện khác)
│   └── tabler.min.js      # File JS được Vite build (entry point)
├── jquery/                # jQuery assets
│   ├── jquery.min.js      # jQuery minified
│   ├── jquery.min.map     # Source map
│   ├── jquery.js          # jQuery uncompressed
│   ├── jquery.slim.min.js # jQuery slim minified
│   └── jquery.slim.min.map # jQuery slim source map
└── bootstrap/             # Bootstrap assets
    ├── css/               # Bootstrap CSS files
    │   ├── bootstrap.min.css # CSS chính (227KB)
    │   ├── bootstrap.css     # CSS uncompressed
    │   ├── bootstrap-grid.min.css # Grid system only
    │   ├── bootstrap-reboot.min.css # Reboot only
    │   ├── bootstrap-utilities.min.css # Utilities only
    │   └── ... (và các variants RTL, source maps)
    └── js/                # Bootstrap JavaScript files
        ├── bootstrap.bundle.min.js # JS chính với Popper (79KB)
        ├── bootstrap.min.js        # JS không có Popper
        ├── bootstrap.esm.min.js    # ES modules version
        └── ... (và các variants uncompressed, source maps)
└── bootstrap-icons/       # Bootstrap Icons assets
    ├── font/              # Font-based icons
    │   ├── bootstrap-icons.min.css # CSS chính (85KB)
    │   ├── bootstrap-icons.css     # CSS uncompressed
    │   ├── bootstrap-icons.scss    # SCSS source
    │   ├── bootstrap-icons.json    # Icon metadata
    │   └── fonts/         # Font files
    │       ├── bootstrap-icons.woff  # WOFF font (176KB)
    │       └── bootstrap-icons.woff2 # WOFF2 font (131KB)
    └── icons/             # Individual SVG icons (2,078 files)
        ├── 0-circle.svg
        ├── alarm.svg
        ├── arrow-left.svg
        └── ... (2,078 SVG icons)
├── css/                    # Tất cả CSS files của Tabler
│   ├── tabler.min.css     # CSS chính
│   ├── tabler-flags.min.css
│   ├── tabler-payments.min.css
│   ├── tabler-socials.min.css
│   ├── tabler-themes.min.css
│   └── ... (và các variants RTL, source maps)
├── js/                     # Tất cả JavaScript files của Tabler
│   ├── tabler.min.js      # JS chính
│   ├── tabler-theme.min.js
│   └── ... (và các variants ESM, source maps)
├── img/                    # Tất cả hình ảnh
│   ├── flags/             # Cờ các quốc gia
│   ├── payments/          # Icons thanh toán
│   └── social/            # Icons mạng xã hội
├── libs/                   # Thư viện bên thứ 3
│   ├── apexcharts/
│   ├── bootstrap/
│   ├── fullcalendar/
│   └── ... (20+ thư viện khác)
└── tabler.min.js          # File JS được Vite build (entry point)
```

## Scripts có sẵn

### `npm run build`

Chạy Vite build để sao chép files từ `node_modules` ra `public/vendor/` (bao gồm Tabler, jQuery, Bootstrap và Bootstrap Icons).

### `npm run clean`

Xóa toàn bộ nội dung trong `public/vendor/tabler/`, `public/vendor/jquery/`, `public/vendor/bootstrap/` và `public/vendor/bootstrap-icons/`.

### `npm run clean:tabler`

Chỉ xóa nội dung trong `public/vendor/tabler/`.

### `npm run clean:jquery`

Chỉ xóa nội dung trong `public/vendor/jquery/`.

### `npm run clean:bootstrap`

Chỉ xóa nội dung trong `public/vendor/bootstrap/`.

### `npm run clean:bootstrap-icons`

Chỉ xóa nội dung trong `public/vendor/bootstrap-icons/`.

### `npm run build:clean`

Xóa và build lại từ đầu.

## Cách sử dụng

### 1. Build lần đầu
```bash
npm run build:clean
```

### 2. Kiểm tra tính toàn vẹn
```bash
npm run verify
```

### 3. Update khi có phiên bản mới của @tabler/core
```bash
npm update @tabler/core
npm run build:verify
```

## Cấu hình Vite

File `vite.config.js` được cấu hình để:

1. **Entry point**: Sử dụng `tabler.min.js` làm entry point chính
2. **Output**: Tạo file `tabler.min.js` trong thư mục đích
3. **Plugin copy**: Sao chép toàn bộ thư mục `dist` của `@tabler/core`
4. **Logging**: Hiển thị chi tiết quá trình sao chép

## Lưu ý quan trọng

- **Không chỉnh sửa trực tiếp** các files trong `public/vendor/tabler/` vì chúng sẽ bị ghi đè khi build
- **Luôn sử dụng** `npm run build:clean` khi muốn đảm bảo có bản copy mới nhất
- **Kiểm tra tính toàn vẹn** bằng `npm run verify` sau mỗi lần build
- File `tabler.min.js` ở root của `public/vendor/tabler/` là do Vite tạo ra, khác với file gốc trong `js/tabler.min.js`

## Troubleshooting

### Lỗi "You must supply options.input to rollup"
Đảm bảo file `node_modules/@tabler/core/dist/js/tabler.min.js` tồn tại.

### Files bị thiếu sau khi build
Chạy `npm run verify` để kiểm tra chi tiết và `npm run build:clean` để build lại.

### Cập nhật không có hiệu lực
Chạy `npm run clean` trước khi build để đảm bảo xóa hết files cũ.

## Thống kê

- **Tabler Core**: 1,962 files (CSS, JS, images, third-party libs)
- **jQuery**: 5 files (JS và source maps)
- **Bootstrap**: 44 files (CSS, JS và source maps)
- **Bootstrap Icons**: 2,084 files (font files + SVG icons)
- **Tổng cộng**: 4,095 files được sao chép
