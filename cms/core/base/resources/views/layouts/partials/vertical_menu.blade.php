<aside class="navbar navbar-vertical navbar-expand-lg" data-bs-theme="dark">
    <div class="container-fluid">
        <!-- BEGIN NAVBAR TOGGLER -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar-menu" aria-controls="sidebar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <!-- END NAVBAR TOGGLER -->
        <!-- BEGIN NAVBAR LOGO -->
        <div class="navbar-brand navbar-brand-autodark">
            <a href="{{ route('dashboard.index') }}">
                <img src="{{ asset('cms/img/logo-cadami.png') }}" alt="AdminCP" class="navbar-brand-image">
            </a>
        </div>
        <!-- END NAVBAR LOGO -->

        <div class="collapse navbar-collapse" id="sidebar-menu">
            <!-- BEGIN NAVBAR MENU -->
            <ul class="navbar-nav pt-lg-3">
                @foreach ($menus = DashboardMenu::getAll() as $menu)
                @php $menu = apply_filters(BASE_FILTER_DASHBOARD_MENU, $menu); @endphp
                <li class="nav-item @if ($menu['active']) active @endif @if (isset($menu['children']) && count($menu['children'])) dropdown @endif">
                    <a class="nav-link @if ($menu['active']) show @endif @if (isset($menu['children']) && count($menu['children'])) dropdown-toggle @endif" href="{{ $menu['url'] }}" @if (isset($menu['children']) && count($menu['children'])) data-bs-toggle="dropdown" @endif data-bs-auto-close="false" role="button" aria-expanded="true">
                        @if ($menu['icon'])
                        <span class="nav-link-icon d-md-none d-lg-inline-block">
                            <i class="icon {{ $menu['icon'] }}"></i>
                        </span>
                        @endif
                        <span class="nav-link-title">
                            {{ !is_array(trans($menu['name'])) ? trans($menu['name']) : null }}
                            {!! apply_filters(BASE_FILTER_APPEND_MENU_NAME, null, $menu['id']) !!}
                        </span>
                    </a>
                    @if (isset($menu['children']) && count($menu['children']))
                    <div class="dropdown-menu @if ($menu['active']) show @endif" @if ($menu['active']) data-bs-popper="static" @endif>
                        <div class="dropdown-menu-columns">
                            <div class="dropdown-menu-column">
                                @foreach ($menu['children'] as $item)
                                <a class="dropdown-item @if ($item['active']) active @endif" href="{{ $item['url'] }}" id="{{ $item['id'] }}">
                                    @if ($item['icon'])
                                    <i class="{{ $item['icon'] }}"></i>
                                    @endif
                                    {{ trans($item['name']) }}
                                    {!! apply_filters(BASE_FILTER_APPEND_MENU_NAME, null, $item['id']) !!}
                                </a>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </li>
                @endforeach
            </ul>
            <!-- END NAVBAR MENU -->
        </div>
    </div>
</aside>
