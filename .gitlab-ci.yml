stages:
  - deploy

cache:
  paths:
    - vendor/

deploy_dev:
  stage: deploy
  image: php:8.4-fpm
  before_script:
    - apt-get update -yqq
    - apt-get clean
    - apt-get install -yqq git libpq-dev libcurl4-gnutls-dev libicu-dev libvpx-dev libjpeg-dev libpng-dev libxpm-dev zlib1g-dev libfreetype6-dev libxml2-dev libexpat1-dev libbz2-dev libgmp3-dev libldap2-dev unixodbc-dev libsqlite3-dev libaspell-dev libsnmp-dev libpcre3-dev libtidy-dev libonig-dev libzip-dev
    # Install PHP extensions
    - docker-php-ext-install mbstring pdo_pgsql curl intl gd xml zip bz2 opcache exif
    # Install and run Composer
    - curl -sS https://getcomposer.org/installer | php
    - php composer.phar install
    - echo "DONE COMPOSER!"
    # Add the private SSH key to the build environment
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - touch ~/.ssh/config
    - touch ~/.ssh/known_hosts
    - chmod -R 400 ~/.ssh
    - ssh-keyscan -t rsa -H dev.cadami.site >> ~/.ssh/known_hosts
    - '[[ -f /.dockerinit ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - echo "$DEV_PUBLIC_SSH_KEY" > ~/.ssh/id_rsa.pub
    - echo "$DEV_PRIVATE_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 400 ~/.ssh/id_rsa
    - chmod 764 ~/.ssh/id_rsa.pub
    - apt-get install rsync curl -yqq
  script:
    - echo "START SYNC TO WEB SERVER..."
    - ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no <EMAIL>
    - rsync -avz -e 'ssh' --exclude={'.git','README.md','.gitlab-ci.yml','composer.phar','.env.example','CHANGLOG','docker','docker-compose.yml','zcadami','.gitignore','.gitattributes','.editorconfig','tests'} ./ <EMAIL>:/www/wwwroot/dinocms.cadami.site
    - ssh -i ~/.ssh/id_rsa <EMAIL> '/www/server/php/83/bin/php /www/wwwroot/dinocms.cadami.site/artisan migrate && /www/server/php/83/bin/php /www/wwwroot/dinocms.cadami.site/artisan optimize'

    - echo "DEPLOY SUCCESS TO WEB SERVER DEV! Check at https://dinocms.cadami.site"
  retry: 1
  only:
    - develop
